{"name": "@samatransport/control-app", "version": "0.1.0", "private": true, "description": "Application Control - Centre de commande et d'administration SAMATRANSPORT", "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next && rm -rf .turbo"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@samatransport/lib-core": "workspace:*", "@samatransport/ui": "workspace:*", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-query": "^5.14.2", "@tanstack/react-query-devtools": "^5.77.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "next": "^14.0.4", "next-auth": "^4.24.5", "next-intl": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}}