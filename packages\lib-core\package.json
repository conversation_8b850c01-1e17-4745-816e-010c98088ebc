{"name": "@samatransport/lib-core", "version": "0.1.0", "description": "Bibliothèque de logique métier et utilitaires partagés pour l'écosystème SAMATRANSPORT", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./auth": "./src/auth/index.ts", "./api": "./src/api/index.ts", "./i18n": "./src/i18n/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts"}, "scripts": {"build": "tsc --noEmit", "lint": "eslint src/", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "clean": "rm -rf .turbo && rm -rf node_modules"}, "dependencies": {"@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "next-auth": "^4.24.5", "next-intl": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "zustand": "^4.4.7", "@tanstack/react-query": "^5.14.2"}, "devDependencies": {"@types/node": "^20.10.5", "typescript": "^5.3.3", "vitest": "^1.0.4", "@testing-library/jest-dom": "^6.1.5"}, "peerDependencies": {"react": "^18.0.0", "next": "^14.0.0"}, "files": ["src"]}