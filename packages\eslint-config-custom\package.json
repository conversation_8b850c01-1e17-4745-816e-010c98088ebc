{"name": "@samatransport/eslint-config-custom", "version": "0.1.0", "description": "Configuration ESLint partagée pour l'écosystème SAMATRANSPORT", "main": "index.js", "files": ["index.js", "base.js", "next.js", "react.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.29.1"}, "peerDependencies": {"eslint": "^8.0.0", "typescript": "^5.0.0"}}