import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import type { Database } from '@samatransport/lib-core';

/**
 * Client Supabase pour les composants client
 */
export const createClientSupabase = () => createClientComponentClient<Database>();

/**
 * Client Supabase pour les composants serveur
 */
export const createServerSupabase = () => createServerComponentClient<Database>({ cookies });

/**
 * Client Supabase avec service role (pour les opérations admin)
 */
export const createServiceSupabase = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

/**
 * Types pour l'authentification
 */
export type AuthUser = {
  id: string;
  email: string;
  user_metadata: {
    full_name?: string;
    avatar_url?: string;
  };
};

/**
 * Utilitaires pour l'authentification
 */
export const auth = {
  /**
   * Vérifie si l'utilisateur a un rôle spécifique
   */
  hasRole: (user: AuthUser, role: string): boolean => {
    // TODO: Implémenter la vérification des rôles depuis la base de données
    return true;
  },

  /**
   * Vérifie si l'utilisateur a une permission spécifique
   */
  hasPermission: (user: AuthUser, permission: string): boolean => {
    // TODO: Implémenter la vérification des permissions depuis la base de données
    return true;
  },
};
