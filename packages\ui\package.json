{"name": "@samatransport/ui", "version": "0.1.0", "description": "SAMATRANSPORT Design System - Composants UI réutilisables", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./styles": "./src/styles/globals.css"}, "scripts": {"dev": "storybook dev -p 6006", "build": "tsc --noEmit", "build-storybook": "storybook build", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf storybook-static"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@storybook/react": "^7.6.4", "@storybook/nextjs": "^7.6.4", "@storybook/addon-essentials": "^7.6.4", "@storybook/addon-interactions": "^7.6.4", "@storybook/addon-links": "^7.6.4", "@storybook/blocks": "^7.6.4", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "files": ["src"]}