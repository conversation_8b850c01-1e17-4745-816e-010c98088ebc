'use client';

import { useState, useEffect } from 'react';
import { agencyService, type AgencyFilters as IAgencyFilters } from '@/services/agencyService';

interface AgencyFiltersProps {
  onFiltersChange: (filters: IAgencyFilters) => void;
}

/**
 * Composant de filtres pour la liste des agences
 */
export function AgencyFilters({ onFiltersChange }: AgencyFiltersProps) {
  const [filters, setFilters] = useState<IAgencyFilters>({});
  const [countries, setCountries] = useState<string[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  // Charger la liste des pays
  useEffect(() => {
    const loadCountries = async () => {
      try {
        const countryList = await agencyService.getCountries();
        setCountries(countryList);
      } catch (error) {
        console.error('Erreur lors du chargement des pays:', error);
      }
    };

    loadCountries();
  }, []);

  /**
   * Met à jour les filtres et notifie le parent
   */
  const updateFilters = (newFilters: Partial<IAgencyFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  /**
   * Remet à zéro tous les filtres
   */
  const resetFilters = () => {
    const emptyFilters = {};
    setFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  /**
   * Vérifie si des filtres sont actifs
   */
  const hasActiveFilters = () => {
    return Object.values(filters).some(value => 
      value !== undefined && value !== '' && value !== null
    );
  };

  const currencies = [
    { value: 'XOF', label: 'CFA (XOF)' },
    { value: 'GNF', label: 'Franc Guinéen (GNF)' },
    { value: 'LRD', label: 'Dollar Libérien (LRD)' },
    { value: 'SLL', label: 'Leone (SLL)' },
    { value: 'EUR', label: 'Euro (EUR)' },
    { value: 'USD', label: 'Dollar US (USD)' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200">
      <div className="p-4">
        {/* Barre de recherche principale */}
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Rechercher par nom, code ou ville..."
                value={filters.search || ''}
                onChange={(e) => updateFilters({ search: e.target.value })}
                className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-lg leading-5 bg-white placeholder-neutral-500 focus:outline-none focus:placeholder-neutral-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Bouton pour étendre/réduire les filtres */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={`inline-flex items-center px-4 py-2 border rounded-lg text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
              hasActiveFilters() || isExpanded
                ? 'border-primary-300 text-primary-700 bg-primary-50 hover:bg-primary-100'
                : 'border-neutral-300 text-neutral-700 bg-white hover:bg-neutral-50'
            }`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            Filtres
            {hasActiveFilters() && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                {Object.values(filters).filter(v => v !== undefined && v !== '' && v !== null).length}
              </span>
            )}
          </button>

          {/* Bouton de réinitialisation */}
          {hasActiveFilters() && (
            <button
              onClick={resetFilters}
              className="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Effacer
            </button>
          )}
        </div>

        {/* Filtres étendus */}
        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-neutral-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Filtre par pays */}
              <div>
                <label htmlFor="country-filter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Pays
                </label>
                <select
                  id="country-filter"
                  value={filters.country || ''}
                  onChange={(e) => updateFilters({ country: e.target.value || undefined })}
                  className="block w-full px-3 py-2 border border-neutral-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Tous les pays</option>
                  {countries.map((country) => (
                    <option key={country} value={country}>
                      {country}
                    </option>
                  ))}
                </select>
              </div>

              {/* Filtre par devise */}
              <div>
                <label htmlFor="currency-filter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Devise
                </label>
                <select
                  id="currency-filter"
                  value={filters.currency || ''}
                  onChange={(e) => updateFilters({ currency: e.target.value || undefined })}
                  className="block w-full px-3 py-2 border border-neutral-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Toutes les devises</option>
                  {currencies.map((currency) => (
                    <option key={currency.value} value={currency.value}>
                      {currency.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Filtre par statut */}
              <div>
                <label htmlFor="status-filter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Statut
                </label>
                <select
                  id="status-filter"
                  value={filters.isActive === undefined ? '' : filters.isActive.toString()}
                  onChange={(e) => {
                    const value = e.target.value;
                    updateFilters({ 
                      isActive: value === '' ? undefined : value === 'true' 
                    });
                  }}
                  className="block w-full px-3 py-2 border border-neutral-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Tous les statuts</option>
                  <option value="true">Actives</option>
                  <option value="false">Inactives</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
